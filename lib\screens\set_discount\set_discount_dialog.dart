import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/set_discount_provider.dart';
import '../../models/set_discount.dart';
import '../../utils/currency_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/app_colors.dart';

import '../../widgets/confirmation_dialog.dart';
import 'set_discount_form_dialog.dart';

/// 세트 할인 관리 다이얼로그
class SetDiscountDialog extends ConsumerStatefulWidget {
  const SetDiscountDialog({super.key});

  /// 다이얼로그 표시 메서드
  static Future<void> show(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => const SetDiscountDialog(),
    );
  }

  @override
  ConsumerState<SetDiscountDialog> createState() => _SetDiscountDialogState();
}

class _SetDiscountDialogState extends ConsumerState<SetDiscountDialog> {
  Future<void> _onBackPressed() async {
    if (mounted) Navigator.of(context).pop();
  }

  List<SetDiscount> _reorderableSetDiscounts = [];

  @override
  void initState() {
    super.initState();
    // 다이얼로그 진입 시 데이터 로드 및 상태 초기화
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final notifier = ref.read(setDiscountNotifierProvider.notifier);

      // 업데이트 상태 초기화
      final currentState = ref.read(setDiscountNotifierProvider);
      if (currentState.isUpdating) {
        notifier.state = currentState.copyWith(isUpdating: false);
      }

      // 데이터 로드 후 메모리에 복사
      await notifier.loadSetDiscounts();
      await notifier.loadActiveSetDiscounts();

      // 로드 완료 후 메모리에 복사
      if (mounted) {
        _loadSetDiscounts();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 세트 할인 목록 로드 (임시 메모리로)
  void _loadSetDiscounts() {
    final state = ref.read(setDiscountNotifierProvider);
    setState(() {
      _reorderableSetDiscounts = List.from(state.setDiscounts);
    });
  }

  /// 세트 할인 추가 (즉시 저장)
  Future<void> _addSetDiscountToMemory(SetDiscount setDiscount) async {
    try {
      final notifier = ref.read(setDiscountNotifierProvider.notifier);
      final success = await notifier.addSetDiscount(setDiscount);

      if (success) {
        setState(() {
          _reorderableSetDiscounts = List.from(_reorderableSetDiscounts)..add(setDiscount);
        });
      } else {
        if (mounted) {
          ToastUtils.showError(context, '세트 할인 추가에 실패했습니다.');
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '세트 할인 추가 중 오류가 발생했습니다: $e');
      }
    }
  }

  /// 세트 할인 수정 (즉시 저장)
  Future<void> _updateSetDiscountInMemory(SetDiscount updatedSetDiscount) async {
    try {
      final notifier = ref.read(setDiscountNotifierProvider.notifier);
      final success = await notifier.updateSetDiscount(updatedSetDiscount);

      if (success) {
        setState(() {
          final index = _reorderableSetDiscounts.indexWhere((item) => item.id == updatedSetDiscount.id);
          if (index != -1) {
            _reorderableSetDiscounts = List.from(_reorderableSetDiscounts);
            _reorderableSetDiscounts[index] = updatedSetDiscount;
          }
        });
      } else {
        if (mounted) {
          ToastUtils.showError(context, '세트 할인 수정에 실패했습니다.');
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '세트 할인 수정 중 오류가 발생했습니다: $e');
      }
    }
  }







  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(setDiscountLoadingProvider);
    final isUpdating = ref.watch(setDiscountUpdatingProvider);
    final errorMessage = ref.watch(setDiscountErrorProvider);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          await _onBackPressed();
        }
      },
      child: Dialog(
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더 (체크리스트 편집 다이얼로그 스타일)
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                  onPressed: _onBackPressed,
                  tooltip: '뒤로가기',
                ),
                const SizedBox(width: 8),
                Text(
                  '세트 할인 관리',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
                const Spacer(),

              ],
            ),
            // 본문
            Expanded(
              child: Column(
                children: [


                  // 에러 메시지 표시
                  if (errorMessage != null)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red.shade600),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              errorMessage,
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () {
                              ref.read(setDiscountNotifierProvider.notifier).clearError();
                            },
                          ),
                        ],
                      ),
                    ),

                  // 세트 할인 목록
                  Expanded(
                    child: isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _reorderableSetDiscounts.isEmpty
                            ? _buildEmptyState()
                            : _buildSetDiscountList(_reorderableSetDiscounts),
                  ),
                ],
              ),
            ),

            // 하단 버튼 영역 (FloatingActionButton 역할)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: isUpdating ? null : _showAddDialog,
                      icon: isUpdating
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.add),
                      label: const Text('세트 할인 추가'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  /// 빈 상태 위젯
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_offer_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '등록된 세트 할인이 없습니다',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '+ 버튼을 눌러 첫 번째 세트 할인을 추가해보세요',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 세트 할인 목록 위젯
  Widget _buildSetDiscountList(List<SetDiscount> setDiscounts) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: setDiscounts.length,
      itemBuilder: (context, index) {
        final setDiscount = setDiscounts[index];
        return _buildSetDiscountCard(setDiscount);
      },
    );
  }

  /// 세트 할인 카드 위젯
  Widget _buildSetDiscountCard(SetDiscount setDiscount) {
    return Card(
      key: ValueKey(setDiscount.id),
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: setDiscount.isActive 
                ? Colors.green.shade100 
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.local_offer,
            color: setDiscount.isActive 
                ? Colors.green.shade700 
                : Colors.grey.shade600,
            size: 20,
          ),
        ),
        title: Text(
          setDiscount.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: setDiscount.isActive ? null : Colors.grey.shade600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '할인 금액: ${CurrencyUtils.formatCurrency(setDiscount.discountAmount)}',
              style: TextStyle(
                color: setDiscount.isActive ? Colors.green.shade700 : Colors.grey.shade500,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '상품 ${setDiscount.productIds.length}개 포함',
              style: TextStyle(
                color: setDiscount.isActive ? null : Colors.grey.shade500,
              ),
            ),
            if (!setDiscount.isActive)
              Text(
                '비활성화됨',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, setDiscount),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 18),
                  SizedBox(width: 8),
                  Text('수정'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'toggle',
              child: Row(
                children: [
                  Icon(
                    setDiscount.isActive ? Icons.visibility_off : Icons.visibility,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(setDiscount.isActive ? '비활성화' : '활성화'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('삭제', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _showEditDialog(setDiscount),
      ),
    );
  }

  /// 메뉴 액션 처리
  void _handleMenuAction(String action, SetDiscount setDiscount) {
    switch (action) {
      case 'edit':
        _showEditDialog(setDiscount);
        break;
      case 'toggle':
        _toggleSetDiscountActive(setDiscount);
        break;
      case 'delete':
        _showDeleteConfirmation(setDiscount);
        break;
    }
  }

  /// 세트 할인 추가 다이얼로그 표시
  void _showAddDialog() {
    showDialog(
      context: context,
      builder: (context) => SetDiscountFormDialog(
        onSave: _addSetDiscountToMemory,
      ),
    );
  }

  /// 세트 할인 수정 다이얼로그 표시
  void _showEditDialog(SetDiscount setDiscount) {
    showDialog(
      context: context,
      builder: (context) => SetDiscountFormDialog(
        setDiscount: setDiscount,
        onSave: _updateSetDiscountInMemory,
      ),
    );
  }

  /// 세트 할인 활성화/비활성화
  void _toggleSetDiscountActive(SetDiscount setDiscount) async {
    final success = await ref
        .read(setDiscountNotifierProvider.notifier)
        .toggleSetDiscountActive(setDiscount.id!, !setDiscount.isActive);

    if (success && mounted) {
      ToastUtils.showSuccess(
        context,
        setDiscount.isActive
            ? '${setDiscount.name}이(가) 비활성화되었습니다'
            : '${setDiscount.name}이(가) 활성화되었습니다',
      );
    } else if (mounted) {
      ToastUtils.showError(
        context,
        '상태 변경에 실패했습니다',
      );
    }
  }

  /// 세트 할인 삭제 확인 다이얼로그
  void _showDeleteConfirmation(SetDiscount setDiscount) async {
    // null 체크 추가
    if (setDiscount.id == null) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '삭제할 수 없는 세트 할인입니다 (ID가 없음)',
        );
      }
      return;
    }

    final confirmed = await ConfirmationDialog.showDelete(
      context: context,
      title: '세트 할인 삭제',
      message: '${setDiscount.name}을(를) 정말 삭제하시겠습니까?\n이 작업은 되돌릴 수 없습니다.',
      confirmLabel: '삭제',
      cancelLabel: '취소',
    );

    if (confirmed == true && mounted) {
      try {
        final success = await ref
            .read(setDiscountNotifierProvider.notifier)
            .deleteSetDiscount(setDiscount.id!);

        if (success && mounted) {
          // 성공 시 메모리 목록에서도 제거
          setState(() {
            _reorderableSetDiscounts = List.from(_reorderableSetDiscounts)
              ..removeWhere((item) => item.id == setDiscount.id);
          });

          ToastUtils.showSuccess(
            context,
            '${setDiscount.name}이(가) 삭제되었습니다',
          );
        } else if (mounted) {
          ToastUtils.showError(
            context,
            '삭제에 실패했습니다',
          );
        }
      } catch (e) {
        if (mounted) {
          ToastUtils.showError(
            context,
            '삭제 중 오류가 발생했습니다: $e',
          );
        }
      }
    }
  }
}
