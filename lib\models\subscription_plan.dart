import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'subscription_plan.freezed.dart';
part 'subscription_plan.g.dart';

/// 구독 플랜 타입을 정의하는 열거형
enum SubscriptionPlanType {
  /// 무료 플랜
  free,
  /// 플러스 플랜
  plus,
}

/// 구독 플랜 정보를 담는 모델 클래스
@freezed
abstract class SubscriptionPlan with _$SubscriptionPlan {
  const factory SubscriptionPlan({
    /// 플랜 타입
    required SubscriptionPlanType type,
    
    /// 플랜 이름
    required String name,
    
    /// 플랜 설명
    required String description,
    
    /// 월 가격 (원)
    required int monthlyPrice,
    
    /// 연 가격 (원)
    required int yearlyPrice,
    
    /// 최대 행사 수 (null이면 무제한)
    int? maxEvents,
    
    /// 최대 상품 수 (null이면 무제한)
    int? maxProducts,
    
    /// 세트 할인 기능 사용 가능 여부
    @Default(false) bool hasSetDiscountFeature,
    
    /// 서비스 기능 사용 가능 여부
    @Default(false) bool hasServiceFeature,
    
    /// 서버 연동 기능 사용 가능 여부
    @Default(false) bool hasServerSyncFeature,
    
    /// 판매자별 관리 기능 사용 가능 여부
    @Default(false) bool hasSellerManagementFeature,
    
    /// 엑셀 내보내기 기능 사용 가능 여부
    @Default(false) bool hasExcelExportFeature,

    /// PDF 내보내기 기능 사용 가능 여부
    @Default(false) bool hasPdfExportFeature,

    /// 고급 통계 기능 사용 가능 여부
    @Default(false) bool hasAdvancedStatsFeature,

    /// 구글 드라이브 백업 기능 사용 가능 여부
    @Default(false) bool hasGoogleDriveBackupFeature,
  }) = _SubscriptionPlan;

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) => _$SubscriptionPlanFromJson(json);
}

/// 사용자의 구독 상태를 담는 모델 클래스
@freezed
abstract class UserSubscription with _$UserSubscription {
  const factory UserSubscription({
    /// 사용자 ID
    required String userId,
    
    /// 현재 구독 플랜
    required SubscriptionPlanType planType,
    
    /// 구독 시작일
    DateTime? subscriptionStartDate,
    
    /// 구독 만료일 (무료 플랜의 경우 null)
    DateTime? subscriptionEndDate,
    
    /// 구독 상태가 활성화되어 있는지
    @Default(true) bool isActive,
    
    /// 구독 생성일
    required DateTime createdAt,
    
    /// 구독 수정일
    DateTime? updatedAt,
  }) = _UserSubscription;

  factory UserSubscription.fromJson(Map<String, dynamic> json) => _$UserSubscriptionFromJson(json);
  
  /// 무료 플랜으로 기본 구독 생성
  factory UserSubscription.createFree({
    required String userId,
  }) {
    return UserSubscription(
      userId: userId,
      planType: SubscriptionPlanType.free,
      isActive: true,
      createdAt: DateTime.now(),
    );
  }
  
  /// 플러스 플랜으로 구독 생성
  factory UserSubscription.createPlus({
    required String userId,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    return UserSubscription(
      userId: userId,
      planType: SubscriptionPlanType.plus,
      subscriptionStartDate: startDate,
      subscriptionEndDate: endDate,
      isActive: true,
      createdAt: DateTime.now(),
    );
  }


}

/// 구독 플랜 확장 메서드
extension SubscriptionPlanExtensions on SubscriptionPlan {
  /// 무료 플랜인지 확인
  bool get isFree => type == SubscriptionPlanType.free;

  /// 플러스 플랜인지 확인
  bool get isPlus => type == SubscriptionPlanType.plus;


  
  /// 특정 기능이 사용 가능한지 확인
  bool hasFeature(String featureName) {
    switch (featureName) {
      case 'setDiscount':
        return hasSetDiscountFeature;
      case 'service':
        return hasServiceFeature;
      case 'serverSync':
        return hasServerSyncFeature;
      case 'sellerManagement':
        return hasSellerManagementFeature;
      case 'excelExport':
        return hasExcelExportFeature;
      case 'pdfExport':
        return hasPdfExportFeature;
      case 'export': // 기존 호환성을 위해 유지 (엑셀 + PDF 모두)
        return hasExcelExportFeature || hasPdfExportFeature;
      case 'advancedStats':
        return hasAdvancedStatsFeature;
      default:
        return false;
    }
  }
}

/// 사용자 구독 확장 메서드
extension UserSubscriptionExtensions on UserSubscription {
  /// 구독이 유효한지 확인
  bool get isValid {
    if (!isActive) return false;

    // 무료 플랜은 항상 유효
    if (planType == SubscriptionPlanType.free) return true;

    // 플러스 플랜은 만료일 확인
    if (subscriptionEndDate == null) return false;
    return DateTime.now().isBefore(subscriptionEndDate!);
  }
  
  /// 구독이 만료되었는지 확인
  bool get isExpired {
    if (planType == SubscriptionPlanType.free) return false;
    if (subscriptionEndDate == null) return true;
    return DateTime.now().isAfter(subscriptionEndDate!);
  }
  
  /// 구독 만료까지 남은 일수
  int? get daysUntilExpiry {
    if (planType == SubscriptionPlanType.free || subscriptionEndDate == null) {
      return null;
    }

    final now = DateTime.now();
    if (now.isAfter(subscriptionEndDate!)) return 0;

    return subscriptionEndDate!.difference(now).inDays;
  }
  
  /// Firebase 저장용 Map 변환
  Map<String, dynamic> toFirebaseMap() {
    return {
      'userId': userId,
      'planType': planType.toString(),
      'subscriptionStartDate': subscriptionStartDate?.toIso8601String(),
      'subscriptionEndDate': subscriptionEndDate?.toIso8601String(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
  
  /// Firebase에서 로드한 데이터로부터 UserSubscription 생성
  static UserSubscription fromFirebaseMap(Map<String, dynamic> map) {
    return UserSubscription(
      userId: map['userId'] as String? ?? '',
      planType: SubscriptionPlanType.values.firstWhere(
        (e) => e.toString() == map['planType'],
        orElse: () => SubscriptionPlanType.free,
      ),
      subscriptionStartDate: _parseDateTime(map['subscriptionStartDate']),
      subscriptionEndDate: _parseDateTime(map['subscriptionEndDate']),
      isActive: map['isActive'] as bool? ?? true,
      createdAt: _parseDateTime(map['createdAt']) ?? DateTime.now(),
      updatedAt: _parseDateTime(map['updatedAt']),
    );
  }

  /// 안전한 DateTime 파싱 (Timestamp와 String 모두 지원)
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    try {
      // Timestamp 객체인 경우
      if (value is Timestamp) {
        return value.toDate();
      }

      // String인 경우
      if (value is String) {
        return DateTime.parse(value);
      }

      // 기타 경우 null 반환
      return null;
    } catch (e) {
      // 파싱 실패 시 null 반환
      return null;
    }
  }
}

/// 미리 정의된 구독 플랜들
class PredefinedPlans {
  /// 무료 플랜
  static const SubscriptionPlan free = SubscriptionPlan(
    type: SubscriptionPlanType.free,
    name: '무료 플랜',
    description: '소규모 부스에서 가볍게 사용하기 무료 플랜입니다.',
    monthlyPrice: 0,
    yearlyPrice: 0,
    maxEvents: 1,
    maxProducts: 30, // 30개로 변경
    hasSetDiscountFeature: false,
    hasServiceFeature: true, // 서비스 기능 프리 플랜에서도 사용 가능
    hasServerSyncFeature: false,
    hasSellerManagementFeature: false,
    hasExcelExportFeature: false,
    hasPdfExportFeature: false,
    hasAdvancedStatsFeature: false,
  );
  
  /// 플러스 플랜
  static const SubscriptionPlan plus = SubscriptionPlan(
    type: SubscriptionPlanType.plus,
    name: '플러스 플랜',
    description: '규모가 어느정도 있거나 다인 부스에 추천하는 플랜입니다.\n대부분의 부스 전용 프리미엄 기능을 사용할 수 있습니다.',
    monthlyPrice: 2900,
    yearlyPrice: 29000,
    maxEvents: null, // 무제한
    maxProducts: null, // 무제한
    hasSetDiscountFeature: true,
    hasServiceFeature: true,
    hasServerSyncFeature: false, // 동기화 불가
    hasSellerManagementFeature: true, // 판매자 관리 가능
    hasExcelExportFeature: true, // 엑셀 내보내기 가능
    hasPdfExportFeature: true, // PDF 내보내기 가능
    hasAdvancedStatsFeature: true,
    hasGoogleDriveBackupFeature: true, // 구글 드라이브 백업 가능
  );

  /// 플랜 타입으로 플랜 정보 가져오기
  static SubscriptionPlan getPlan(SubscriptionPlanType type) {
    switch (type) {
      case SubscriptionPlanType.free:
        return free;
      case SubscriptionPlanType.plus:
        return plus;
    }
  }

  /// 모든 플랜 목록
  static List<SubscriptionPlan> get allPlans => [free, plus];
}
